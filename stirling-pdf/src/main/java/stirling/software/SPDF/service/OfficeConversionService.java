package stirling.software.SPDF.service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import stirling.software.common.configuration.RuntimePathConfig;
import stirling.software.common.util.ProcessExecutor;
import stirling.software.common.util.ProcessExecutor.ProcessExecutorResult;

@Slf4j
@Service
public class OfficeConversionService {

    @Autowired
    private RuntimePathConfig runtimePathConfig;

    /**
     * 智能选择最佳转换方式
     */
    public byte[] convertToPdf(Path inputFile, String originalFilename) throws IOException, InterruptedException {
        
        // 分析文档复杂度
        DocumentComplexity complexity = analyzeDocumentComplexity(inputFile);
        log.info("文档复杂度分析: {}, 文件: {}", complexity, originalFilename);
        
        // 根据复杂度选择转换策略
        return switch (complexity) {
            case SIMPLE -> convertWithPandoc(inputFile);
            case MEDIUM -> convertWithOptimizedLibreOffice(inputFile);
            case COMPLEX -> convertWithFullLibreOffice(inputFile);
        };
    }
    
    /**
     * 分析文档复杂度
     */
    private DocumentComplexity analyzeDocumentComplexity(Path inputFile) {
        try {
            long fileSize = Files.size(inputFile);
            
            // 简单判断：小于50KB的文件通常比较简单
            if (fileSize < 50 * 1024) {
                return DocumentComplexity.SIMPLE;
            }
            
            // 中等判断：50KB-500KB
            if (fileSize < 500 * 1024) {
                return DocumentComplexity.MEDIUM;
            }
            
            // 复杂文档：大于500KB
            return DocumentComplexity.COMPLEX;
            
        } catch (IOException e) {
            log.warn("无法分析文档复杂度，使用默认策略: {}", e.getMessage());
            return DocumentComplexity.MEDIUM;
        }
    }
    
    /**
     * 使用Pandoc进行快速转换（适合简单文档）
     */
    private byte[] convertWithPandoc(Path inputFile) throws IOException, InterruptedException {
        log.info("使用Pandoc进行快速转换");
        
        Path outputFile = Files.createTempFile("pandoc_output_", ".pdf");
        
        try {
            List<String> command = Arrays.asList(
                "pandoc",
                inputFile.toString(),
                "-o", outputFile.toString(),
                "--pdf-engine=wkhtmltopdf"
            );
            
            ProcessExecutorResult result = ProcessExecutor
                .getInstance(ProcessExecutor.Processes.LIBRE_OFFICE)
                .runCommandWithOutputHandling(command);
                
            if (result.getRc() != 0) {
                log.warn("Pandoc转换失败，回退到LibreOffice");
                return convertWithOptimizedLibreOffice(inputFile);
            }
            
            return Files.readAllBytes(outputFile);
            
        } finally {
            Files.deleteIfExists(outputFile);
        }
    }
    
    /**
     * 使用优化的LibreOffice转换
     */
    private byte[] convertWithOptimizedLibreOffice(Path inputFile) throws IOException, InterruptedException {
        log.info("使用优化的LibreOffice转换");
        
        Path outputFile = Files.createTempFile("libreoffice_output_", ".pdf");
        
        try {
            List<String> command = new ArrayList<>(Arrays.asList(
                runtimePathConfig.getUnoConvertPath(),
                "--port", "2003",
                "--convert-to", "pdf",
                "--filter-options", "ExportFormFields=false;FormsType=0;ExportBookmarks=false;ExportNotes=false",
                inputFile.toString(),
                outputFile.toString()
            ));
            
            ProcessExecutorResult result = ProcessExecutor
                .getInstance(ProcessExecutor.Processes.LIBRE_OFFICE)
                .runCommandWithOutputHandling(command);
                
            if (result.getRc() != 0) {
                throw new IOException("LibreOffice转换失败，返回码: " + result.getRc());
            }
            
            return Files.readAllBytes(outputFile);
            
        } finally {
            Files.deleteIfExists(outputFile);
        }
    }
    
    /**
     * 使用完整的LibreOffice转换（适合复杂文档）
     */
    private byte[] convertWithFullLibreOffice(Path inputFile) throws IOException, InterruptedException {
        log.info("使用完整的LibreOffice转换（保留所有格式）");
        
        Path outputFile = Files.createTempFile("full_libreoffice_output_", ".pdf");
        
        try {
            List<String> command = Arrays.asList(
                runtimePathConfig.getUnoConvertPath(),
                "--port", "2003",
                "--convert-to", "pdf",
                inputFile.toString(),
                outputFile.toString()
            );
            
            ProcessExecutorResult result = ProcessExecutor
                .getInstance(ProcessExecutor.Processes.LIBRE_OFFICE)
                .runCommandWithOutputHandling(command);
                
            if (result.getRc() != 0) {
                throw new IOException("LibreOffice转换失败，返回码: " + result.getRc());
            }
            
            return Files.readAllBytes(outputFile);
            
        } finally {
            Files.deleteIfExists(outputFile);
        }
    }
    
    /**
     * 文档复杂度枚举
     */
    public enum DocumentComplexity {
        SIMPLE,   // 简单文档，使用快速转换
        MEDIUM,   // 中等复杂度，使用优化转换
        COMPLEX   // 复杂文档，使用完整转换
    }
}
